<template>
  <div class="grower-dashboard">
    <div class="page-header">
      <h1>种植者仪表板</h1>
      <p>欢迎回来！这里是您的咖啡豆种植管理中心</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon batch">
          <span class="icon">🌱</span>
        </div>
        <div class="stat-info">
          <h3>总批次数</h3>
          <p class="stat-number">{{ stats.totalBatches || 0 }}</p>
          <span class="stat-trend">+{{ stats.monthlyBatches || 0 }} 本月新增</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon harvest">
          <span class="icon">🚜</span>
        </div>
        <div class="stat-info">
          <h3>已收获批次</h3>
          <p class="stat-number">{{ getStatusCount('harvested') }}</p>
          <span class="stat-trend">{{ getHarvestRate() }}% 收获率</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon shipping">
          <span class="icon">📦</span>
        </div>
        <div class="stat-info">
          <h3>发货记录</h3>
          <p class="stat-number">{{ stats.shippingRecords || 0 }}</p>
          <span class="stat-trend">{{ stats.pendingShipments || 0 }} 待发货</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon blockchain">
          <span class="icon">⛓️</span>
        </div>
        <div class="stat-info">
          <h3>上链记录</h3>
          <p class="stat-number">{{ stats.blockchainRecords || 0 }}</p>
          <span class="stat-trend">{{ stats.pendingChain || 0 }} 待上链</span>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <h3>快速操作</h3>
      <div class="actions-grid">
        <router-link to="/dashboard/grower/batches" class="action-card">
          <div class="action-icon">🌾</div>
          <div class="action-info">
            <h4>种植登记</h4>
            <p>创建和管理咖啡豆批次</p>
          </div>
        </router-link>

        <router-link to="/dashboard/grower/planting" class="action-card">
          <div class="action-icon">📝</div>
          <div class="action-info">
            <h4>种植过程</h4>
            <p>记录种植过程和细节</p>
          </div>
        </router-link>

        <router-link to="/dashboard/grower/harvest" class="action-card">
          <div class="action-icon">🚜</div>
          <div class="action-info">
            <h4>种植收获</h4>
            <p>记录收获情况和质量</p>
          </div>
        </router-link>

        <router-link to="/dashboard/grower/shipping" class="action-card">
          <div class="action-icon">📦</div>
          <div class="action-info">
            <h4>发货管理</h4>
            <p>创建和跟踪发货单</p>
          </div>
        </router-link>

        <router-link to="/dashboard/grower/blockchain" class="action-card">
          <div class="action-icon">⛓️</div>
          <div class="action-info">
            <h4>区块链管理</h4>
            <p>数据上链和验证</p>
          </div>
        </router-link>

        <router-link to="/dashboard/grower/certificates" class="action-card">
          <div class="action-icon">📜</div>
          <div class="action-info">
            <h4>数字证书</h4>
            <p>管理认证和证书</p>
          </div>
        </router-link>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <h3>最近活动</h3>
      <div class="activity-list">
        <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
          <div class="activity-icon" :class="activity.type">
            {{ getActivityIcon(activity.type) }}
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-description">{{ activity.description }}</div>
            <div class="activity-time">{{ formatDateTime(activity.created_at) }}</div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="recentActivities.length === 0" class="empty-activity">
          <div class="empty-icon">📋</div>
          <p>暂无最近活动</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { growerApi } from '@/services/api'

// 响应式数据
const stats = ref({})
const recentActivities = ref([])

// 辅助函数
const getStatusCount = (status: string) => {
  const statusStats = stats.value.batchStats || []
  const statusStat = statusStats.find((s: any) => s.status === status)
  return statusStat ? statusStat.count : 0
}

const getHarvestRate = () => {
  const total = stats.value.totalBatches || 0
  const harvested = getStatusCount('harvested')
  return total > 0 ? Math.round((harvested / total) * 100) : 0
}

const getActivityIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    'batch_created': '🌱',
    'planting_recorded': '📝',
    'harvest_recorded': '🚜',
    'shipment_created': '📦',
    'blockchain_uploaded': '⛓️'
  }
  return iconMap[type] || '📋'
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 数据加载函数
const loadStats = async () => {
  try {
    const response = await growerApi.getStats()
    if (response.data.success) {
      stats.value = response.data.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

const loadRecentActivities = async () => {
  try {
    const response = await growerApi.getRecentActivities()
    if (response.data.success) {
      recentActivities.value = response.data.data.activities || []
    }
  } catch (error) {
    console.error('获取最近活动失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadStats()
  loadRecentActivities()
})
</script>

<style scoped>
.grower-dashboard {
  padding: 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin-bottom: 8px;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
}

.page-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
}

.stat-icon.batch {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.harvest {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-icon.shipping {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.blockchain {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-icon .icon {
  color: white;
}

.stat-info h3 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: #7f8c8d;
  font-weight: 500;
}

.stat-number {
  margin: 0 0 5px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
}

.stat-trend {
  font-size: 0.8rem;
  color: #95a5a6;
}

.quick-actions {
  margin-bottom: 40px;
}

.quick-actions h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  font-size: 1.3rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.action-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 20px;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  color: inherit;
}

.action-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.action-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.action-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.recent-activity {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.recent-activity h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  font-size: 1.3rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.activity-description {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.activity-time {
  color: #95a5a6;
  font-size: 0.8rem;
}

.empty-activity {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}
</style>
