<template>
  <div class="batch-management">
    <div class="page-header">
      <h1>种植登记</h1>
      <p>管理您的咖啡豆批次信息</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <button @click="showCreateDialog" class="create-btn">
        <span class="icon">+</span>
        种植登记
      </button>
      <el-input
        v-model="searchQuery"
        placeholder="搜索批次ID或品种..."
        size="large"
        clearable
        @input="handleSearch"
        class="search-input"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <button @click="loadBatches" class="refresh-btn">
        <span class="icon">↻</span>
        刷新数据
      </button>
    </div>

    <!-- 批次列表 -->
    <div class="batch-list">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading">正在加载批次数据...</div>

      <!-- 批次表格 -->
      <div v-else class="table-container">
        <div v-if="batches.length > 0" class="table-wrapper">
          <table class="data-table">
            <thead>
              <tr>
                <th class="text-center">批次编号</th>
                <th class="text-center">咖啡品种</th>
                <th class="text-center">产地</th>
                <th class="text-center">数量</th>
                <th class="text-center">状态</th>
                <th class="text-center">种植登记时间</th>
                <th class="text-center">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="batch in batches" :key="batch.batch_id" class="table-row">
                <td class="batch-id text-center">{{ batch.batch_id }}</td>
                <td class="variety text-center">{{ batch.variety }}</td>
                <td class="origin text-center">{{ batch.origin_location }}</td>
                <td class="quantity text-center">{{ batch.quantity }} {{ batch.unit }}</td>
                <td class="status text-center">
                  <span class="status-tag" :class="getStatusClass(getBatchActualStatus(batch))">
                    {{ getStatusName(getBatchActualStatus(batch)) }}
                  </span>
                </td>
                <td class="date text-center">{{ formatDate(batch.created_at) }}</td>
                <td class="actions text-center">
                  <button
                    @click="editBatch(batch)"
                    class="action-btn edit-btn">
                    编辑
                  </button>
                  <button
                    v-if="!hasPlantingRecords(batch.batch_id)"
                    @click="deleteBatch(batch)"
                    class="action-btn delete-btn">
                    删除
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 空状态 -->
        <div v-if="batches.length === 0" class="empty-state">
          <p>暂无种植物数据</p>
        </div>
      </div>


    </div>

    <!-- 创建批次对话框 -->
    <el-dialog v-model="createDialogVisible" :title="isEditMode ? '编辑种植物' : '种植登记'" width="600px">
      <el-form :model="createForm" label-width="100px" :rules="formRules" ref="createFormRef">
        <!-- 编辑模式下显示批次号 -->
        <el-form-item v-if="isEditMode" label="批次号" prop="batch_id">
          <el-input
            v-model="createForm.batch_id"
            placeholder="批次号不可修改"
            readonly
            class="edit-mode-input"
          />
          <div class="batch-id-info">
            <el-text type="warning" size="small">
              ⚠️ 批次号不可二次修改，确保溯源完整性
            </el-text>
          </div>
        </el-form-item>
        <el-form-item label="咖啡品种" prop="variety">
          <el-input
            v-model="createForm.variety"
            placeholder="请输入咖啡品种（如：阿拉比卡、瑰夏等）"
            @blur="onVarietyBlur"
            @input="onVarietyInput"
          />
        </el-form-item>
        <el-form-item label="产地" prop="origin_location">
          <el-input
            v-model="createForm.origin_location"
            placeholder="请输入产地，或等待系统自动匹配"
          />
          <div v-if="suggestedOrigin" class="origin-suggestion">
            <el-tag type="success" size="small">
              <el-icon><Check /></el-icon>
              已自动匹配产地，无需输入
            </el-tag>
          </div>
        </el-form-item>

        <el-form-item label="种植数量" prop="quantity">
          <div style="display: flex; gap: 10px;">
            <el-input-number
              v-model="createForm.quantity"
              :min="0"
              :precision="2"
              placeholder="请输入种植数量"
              style="flex: 1;"
            />
            <el-select v-model="createForm.unit" style="width: 100px;">
              <el-option label="公斤" value="kg" />
              <el-option label="吨" value="ton" />
              <el-option label="亩" value="acre" />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="createForm.notes"
            type="textarea"
            :rows="4"
            placeholder="请输入备注信息（如：认证信息、特殊要求等）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="createBatch" :loading="submitting">创建批次</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { Search, ArrowDown, Close, Check } from '@element-plus/icons-vue'
import { growerApi } from '@/services/api'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const batches = ref([])
const searchQuery = ref('')

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const suggestedOrigin = ref('')

// 存储批次的种植记录状态和收获记录状态
const batchPlantingStatus = ref<Record<string, boolean>>({})
const batchHarvestStatus = ref<Record<string, boolean>>({})

// 对话框状态
const createDialogVisible = ref(false)
const createFormRef = ref<FormInstance>()

// 常见咖啡品种参考
const coffeeVarieties = ref([
  { name: '阿拉比卡', origin: '云南普洱', description: '口感醇厚，酸度适中' },
  { name: '铁皮卡', origin: '云南德宏', description: '经典品种，风味平衡' },
  { name: '波旁', origin: '云南临沧', description: '甜度高，果香浓郁' },
  { name: '卡杜拉', origin: '云南保山', description: '酸度明亮，口感清爽' },
  { name: '卡蒂姆', origin: '云南思茅', description: '抗病性强，产量高' },
  { name: '瑰夏', origin: '巴拿马', description: '花香浓郁，茶感明显' },
  { name: '蓝山', origin: '牙买加', description: '口感温和，香气独特' },
  { name: '耶加雪菲', origin: '埃塞俄比亚', description: '柑橘酸香，花果香' },
  { name: '曼特宁', origin: '印度尼西亚', description: '草本香气，口感厚重' },
  { name: '云南小粒咖啡', origin: '云南西双版纳', description: '香气清雅，口感柔和' }
])

// 表单数据
const createForm = reactive({
  batch_id: '',
  variety: '',
  origin_location: '',
  quantity: '',
  unit: 'kg',
  notes: ''
})

// 编辑模式状态
const isEditMode = ref(false)

// 表单验证规则
const formRules = {
  variety: [{ required: true, message: '请选择咖啡品种', trigger: 'change' }],
  origin_location: [{ required: true, message: '请输入产地', trigger: 'blur' }],
  quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }]
}

// 辅助函数
const getStatusName = (status: string) => {
  const names: Record<string, string> = {
    pending: '待种植',
    planted: '种植中',
    harvested: '已收获',
    processed: '已加工',
    distributed: '已分销',
    sold: '已售出'
  }
  return names[status] || status
}

// 获取状态样式类
const getStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': 'status-pending',
    'planted': 'status-planted',
    'harvested': 'status-harvested',
    'processed': 'status-processed',
    'distributed': 'status-distributed',
    'sold': 'status-sold'
  }
  return statusMap[status] || 'status-default'
}

// 检查批次是否有种植记录
const hasPlantingRecords = (batchId: string) => {
  return batchPlantingStatus.value[batchId] || false
}

// 检查批次是否有收获记录
const hasHarvestRecords = (batchId: string) => {
  return batchHarvestStatus.value[batchId] || false
}

// 获取批次的实际状态（基于记录状态）
const getBatchActualStatus = (batch: any) => {
  const batchId = batch.batch_id
  if (hasHarvestRecords(batchId)) {
    return 'harvested'
  } else if (hasPlantingRecords(batchId)) {
    return 'planted'
  } else {
    return 'pending' // 待种植
  }
}

// 删除批次
const deleteBatch = async (batch: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除批次 ${batch.batch_id} 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 调用删除API
    ElMessage.success('批次删除成功')
    await loadBatches()
  } catch {
    // 用户取消删除
  }
}

const getQualityGradeName = (grade: string) => {
  const gradeMap: Record<string, string> = {
    'premium': '特级',
    'grade_a': '一级',
    'grade_b': '二级',
    'grade_c': '三级'
  }
  return gradeMap[grade] || grade || '未评级'
}

const getQualityTagType = (grade: string) => {
  const typeMap: Record<string, string> = {
    'premium': 'danger',
    'grade_a': 'success',
    'grade_b': 'warning',
    'grade_c': 'info'
  }
  return typeMap[grade] || 'default'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 加载批次记录状态
const loadBatchRecordStatus = async () => {
  try {
    // 获取所有批次的种植记录状态
    const plantingResponse = await growerApi.getPlantingRecords()
    if (plantingResponse.data.success) {
      const plantingRecords = plantingResponse.data.data.records
      const plantingStatus: Record<string, boolean> = {}
      plantingRecords.forEach((record: any) => {
        plantingStatus[record.batch_id] = true
      })
      batchPlantingStatus.value = plantingStatus
    }

    // 获取所有批次的收获记录状态
    const harvestResponse = await growerApi.getAllHarvestRecords()
    if (harvestResponse.data.success) {
      const harvestRecords = harvestResponse.data.data.records
      const harvestStatus: Record<string, boolean> = {}
      harvestRecords.forEach((record: any) => {
        harvestStatus[record.batch_id] = true
      })
      batchHarvestStatus.value = harvestStatus
    }
  } catch (error) {
    console.error('获取批次记录状态失败:', error)
  }
}

// 数据加载函数
const loadBatches = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value
    }

    const response = await growerApi.getBatches(params)
    if (response.data.success) {
      batches.value = response.data.data.batches
      total.value = response.data.data.pagination.total

      // 加载批次记录状态
      await loadBatchRecordStatus()
    }
  } catch (error) {
    console.error('获取批次列表失败:', error)
    ElMessage.error('获取批次列表失败')
  } finally {
    loading.value = false
  }
}

// 事件处理函数
const handleSearch = () => {
  currentPage.value = 1
  loadBatches()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadBatches()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadBatches()
}

// 品种输入和匹配处理
const onVarietyInput = () => {
  // 清除之前的自动匹配状态
  if (suggestedOrigin.value) {
    suggestedOrigin.value = ''
  }
}

const onVarietyBlur = () => {
  // 用户输入完成后，尝试匹配品种
  const inputVariety = createForm.variety.trim()
  if (inputVariety) {
    matchOrigin(inputVariety)
  }
}

const matchOrigin = (varietyName: string) => {
  const selectedVariety = coffeeVarieties.value.find(v =>
    v.name.toLowerCase() === varietyName.toLowerCase()
  )
  if (selectedVariety) {
    suggestedOrigin.value = selectedVariety.origin
    createForm.origin_location = selectedVariety.origin
    ElMessage.success(`已自动匹配产地：${selectedVariety.origin}`)
  } else {
    suggestedOrigin.value = ''
    // 不清空用户已输入的产地
  }
}



// 批次管理函数
const showCreateDialog = () => {
  // 设置为创建模式
  isEditMode.value = false

  Object.assign(createForm, {
    batch_id: '', // 创建时不需要批次号，后端会自动生成
    variety: '',
    origin_location: '',
    quantity: '',
    unit: 'kg',
    notes: ''
  })
  suggestedOrigin.value = ''
  createDialogVisible.value = true
  // 创建模式下不需要生成批次号
}

const createBatch = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()
    submitting.value = true

    // 创建批次数据，不包含batch_id（后端自动生成）
    const batchData = {
      variety: createForm.variety,
      origin_location: createForm.origin_location,
      quantity: createForm.quantity,
      unit: createForm.unit,
      notes: createForm.notes
    }

    const response = await growerApi.createBatch(batchData)
    if (response.data.success) {
      ElMessage.success(`批次创建成功！批次号：${response.data.data.batch_id}`)
      createDialogVisible.value = false
      loadBatches()
    } else {
      ElMessage.error(response.data.message || '创建失败')
    }
  } catch (error: any) {
    console.error('创建批次失败:', error)
    ElMessage.error(error.response?.data?.message || '创建批次失败')
  } finally {
    submitting.value = false
  }
}



// 编辑批次
const editBatch = (batch: any) => {
  // 检查是否已开始种植
  if (hasPlantingRecords(batch.batch_id)) {
    ElMessage.warning('该批次已开始种植，不允许修改基本信息')
    return
  }

  // 设置编辑模式
  isEditMode.value = true

  // 填充表单数据
  Object.assign(createForm, {
    batch_id: batch.batch_id,
    variety: batch.variety,
    origin_location: batch.origin_location,
    quantity: batch.quantity,
    unit: batch.unit,
    notes: batch.notes
  })

  createDialogVisible.value = true
}

// 处理更多操作
const handleAction = (command: string, batch: any) => {
  switch (command) {
    case 'duplicate':
      duplicateBatch(batch)
      break
    case 'export':
      exportBatchData(batch)
      break
    case 'delete':
      deleteBatch(batch)
      break
  }
}

// 复制批次
const duplicateBatch = (batch: any) => {
  // 设置为创建模式
  isEditMode.value = false

  Object.assign(createForm, {
    batch_id: '', // 创建模式下不需要批次号
    variety: batch.variety,
    origin_location: batch.origin_location,
    quantity: batch.quantity,
    unit: batch.unit,
    notes: `复制自批次：${batch.batch_id}`
  })
  createDialogVisible.value = true
  ElMessage.success('已复制批次信息，请修改后保存')
}

// 导出批次数据
const exportBatchData = (batch: any) => {
  const data = {
    批次号: batch.batch_id,
    咖啡品种: batch.variety,
    产地: batch.origin_location,
    种植日期: batch.planting_date || '未设置',
    数量: `${batch.quantity} ${batch.unit}`,
    状态: getStatusName(batch.status),
    备注: batch.notes || '无',
    创建时间: batch.created_at || '未知'
  }

  const jsonStr = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `批次数据_${batch.batch_id}.json`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('批次数据已导出')
}



// 生命周期
onMounted(() => {
  loadBatches()
})
</script>

<style scoped>
.batch-management {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin-bottom: 8px;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
}

.page-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 1rem;
}

.action-bar {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  gap: 24px;
}

.create-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.create-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.create-btn .icon {
  font-size: 16px;
  font-weight: bold;
}

.search-input {
  width: 350px;
  flex-shrink: 0;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.search-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.search-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.refresh-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.refresh-btn .icon {
  font-size: 16px;
}

.batch-list {
  margin-bottom: 20px;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 14px;
}

.table-container {
  margin-bottom: 20px;
}

.table-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table thead {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.data-table th {
  padding: 16px 20px;
  text-align: center;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  white-space: nowrap;
}

.text-center {
  text-align: center !important;
}

.data-table tbody tr {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
  background: #f9fafb;
}

.data-table tbody tr:last-child {
  border-bottom: none;
}

.data-table td {
  padding: 16px 20px;
  color: #374151;
  vertical-align: middle;
}

.batch-id {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #1f2937;
  font-size: 13px;
}

.variety {
  font-weight: 600;
  color: #1f2937;
  font-size: 15px;
}

.origin {
  color: #6b7280;
  font-size: 14px;
}

.quantity {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.status {
  text-align: center;
}

.date {
  color: #6b7280;
  font-size: 13px;
  white-space: nowrap;
}

.actions {
  text-align: center;
  white-space: nowrap;
}

.status-tag {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  display: inline-block;
  min-width: 60px;
  white-space: nowrap;
}

.status-pending { background: #f3f4f6; color: #6b7280; }
.status-planted { background: #fef3c7; color: #92400e; }
.status-harvested { background: #d1fae5; color: #065f46; }
.status-processed { background: #dbeafe; color: #1e40af; }
.status-distributed { background: #e0e7ff; color: #3730a3; }
.status-sold { background: #fee2e2; color: #991b1b; }
.status-default { background: #f3f4f6; color: #374151; }

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 2px;
}

.edit-btn {
  background: #f59e0b;
  color: white;
}

.edit-btn:hover {
  background: #d97706;
}

.delete-btn {
  background: #ef4444;
  color: white;
}

.delete-btn:hover {
  background: #dc2626;
}

.empty-state {
  text-align: center;
  padding: 60px 40px;
  background: white;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  color: #6b7280;
}

.empty-state p {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
}

.empty-state .create-btn {
  margin-top: 10px;
}



.page-info {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.origin-suggestion {
  margin-top: 8px;
}

.origin-suggestion .el-tag {
  display: inline-flex;
  align-items: center;
  gap: 5px;
}



/* 批次号样式 */
.batch-id-text {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #2c3e50;
}

/* 批次号信息提示 */
.batch-id-info {
  margin-top: 8px;
}

.batch-id-info .el-text {
  font-size: 12px;
}

/* 编辑模式样式 */
.edit-mode-input {
  background-color: #f5f5f5 !important;
  color: #999 !important;
}

.edit-mode-input :deep(.el-input__inner) {
  background-color: #f5f5f5 !important;
  color: #999 !important;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
  }

  .create-btn {
    width: 100%;
    justify-content: center;
  }

  .search-input {
    width: 100%;
  }

  .refresh-btn {
    width: 100%;
    justify-content: center;
  }

  .table-wrapper {
    overflow-x: auto;
  }

  .data-table {
    min-width: 600px;
  }

  .data-table th,
  .data-table td {
    padding: 12px 8px;
    font-size: 12px;
  }

  .batch-id {
    font-size: 11px;
  }

  .variety {
    font-size: 13px;
  }

  .action-btn {
    padding: 4px 8px;
    font-size: 11px;
    margin: 0 1px;
  }
}
</style>
