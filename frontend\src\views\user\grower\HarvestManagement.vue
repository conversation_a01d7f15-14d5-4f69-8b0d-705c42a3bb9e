<template>
  <div class="harvest-management">
    <div class="page-header">
      <h1>种植收获</h1>
      <p>管理已完成种植的批次收获信息</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="showPlantingRecords" :loading="loadingPlanting">
        <el-icon><Plus /></el-icon>
        选择种植记录进行收获
      </el-button>
      <div class="search-filters">
        <el-select v-model="selectedBatch" placeholder="所有批次" style="width: 200px; margin-right: 10px;" @change="loadHarvests">
          <el-option label="所有批次" value="" />
          <el-option
            v-for="batch in batches"
            :key="batch.batch_id"
            :label="`${batch.batch_id} - ${batch.variety}`"
            :value="batch.batch_id"
          />
        </el-select>
        <el-button @click="refreshData" :loading="loading">刷新数据</el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table :data="harvests" v-loading="loading" stripe style="width: 100%">
        <el-table-column prop="batch_id" label="批次编号" width="180" />
        <el-table-column prop="variety" label="咖啡品种" width="120" />
        <el-table-column prop="harvest_date" label="收获日期" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.harvest_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="harvest_quantity" label="收获数量" width="120">
          <template #default="scope">
            {{ scope.row.harvest_quantity }} {{ scope.row.harvest_unit || 'kg' }}
          </template>
        </el-table-column>
        <el-table-column prop="quality_assessment" label="质量评估" width="120" />
        <el-table-column prop="moisture_content" label="含水量" width="100">
          <template #default="scope">
            {{ scope.row.moisture_content ? scope.row.moisture_content + '%' : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="processing_method" label="处理方式" width="120" />
        <el-table-column prop="created_at" label="创建时间" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewDetail(scope.row)">
              详情
            </el-button>
            <el-button type="success" size="small" @click="editHarvest(scope.row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteHarvest(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 收获记录列表 -->
    <div v-loading="loading" class="records-grid">
      <div v-for="harvest in harvests" :key="harvest.id" class="record-card">
        <div class="record-header">
          <div class="batch-info">
            <el-tag type="info" size="small">{{ harvest.batch_id || 'N/A' }}</el-tag>
            <span class="variety">收获记录</span>
          </div>
          <div class="record-date">
            {{ formatDate(harvest.harvest_date) }}
          </div>
        </div>

        <div class="record-content">
          <div class="record-item" v-if="harvest.harvest_quantity">
            <span class="label">收获数量:</span>
            <span class="value highlight">{{ harvest.harvest_quantity }} {{ harvest.harvest_unit || 'kg' }}</span>
          </div>
          <div class="record-item" v-if="harvest.quality_assessment">
            <span class="label">质量评估:</span>
            <span class="value">{{ harvest.quality_assessment }}</span>
          </div>
          <div class="record-item" v-if="harvest.moisture_content">
            <span class="label">含水量:</span>
            <span class="value">{{ harvest.moisture_content }}%</span>
          </div>
          <div class="record-item" v-if="harvest.processing_method">
            <span class="label">处理方式:</span>
            <span class="value">{{ harvest.processing_method }}</span>
          </div>
          <div class="record-item full-width" v-if="harvest.notes">
            <span class="label">备注:</span>
            <span class="value">{{ harvest.notes }}</span>
          </div>
        </div>

        <div class="record-actions">
          <el-button type="primary" size="small" @click="viewDetail(harvest)">
            详情
          </el-button>
          <el-button type="success" size="small" @click="editHarvest(harvest)">
            编辑
          </el-button>
          <el-button type="danger" size="small" @click="deleteHarvest(harvest)">
            删除
          </el-button>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="harvests.length === 0 && !loading" class="empty-state">
        <div class="empty-icon">🚜</div>
        <h3>暂无收获记录</h3>
        <p v-if="plantingRecords.length === 0">
          暂无可收获的种植记录，请先在种植记录中完成种植
        </p>
        <p v-else>
          选择已完成的种植记录进行收获登记
        </p>
        <el-button
          type="primary"
          @click="showPlantingRecords"
          :loading="loadingPlanting"
        >
          选择种植记录进行收获
        </el-button>
      </div>
    </div>

    <!-- 种植记录选择对话框 -->
    <el-dialog
      v-model="showPlantingDialog"
      title="选择种植记录进行收获"
      width="800px"
    >
      <el-table :data="plantingRecords" v-loading="loadingPlanting" @row-click="selectPlantingRecord">
        <el-table-column prop="batch_id" label="批次编号" width="120" />
        <el-table-column prop="variety" label="咖啡品种" width="120" />
        <el-table-column prop="planting_date" label="种植日期" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.planting_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="planting_area" label="种植面积" width="100">
          <template #default="scope">
            {{ scope.row.planting_area }} {{ scope.row.area_unit || '亩' }}
          </template>
        </el-table-column>
        <el-table-column prop="planting_quantity" label="种植数量" width="100">
          <template #default="scope">
            {{ scope.row.planting_quantity }} {{ scope.row.quantity_unit || '株' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" size="small" @click="selectPlantingRecord(scope.row)">
              选择收获
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="plantingRecords.length === 0 && !loadingPlanting" style="text-align: center; padding: 40px; color: #909399;">
        暂无可收获的种植记录
      </div>
    </el-dialog>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="showDialog"
      :title="isEditing ? '编辑收获记录' : '记录收获'"
      width="600px"
      :before-close="closeDialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
        label-position="left"
      >
        <el-form-item label="批次编号">
          <el-input v-model="form.batch_id" readonly style="width: 100%;" />
        </el-form-item>

        <el-form-item label="咖啡品种">
          <el-input v-model="form.variety" readonly style="width: 100%;" />
        </el-form-item>

        <el-form-item label="收获日期" prop="harvest_date">
          <el-date-picker
            v-model="form.harvest_date"
            type="date"
            placeholder="选择收获日期"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item label="收获数量" prop="harvest_quantity">
          <el-input-number
            v-model="form.harvest_quantity"
            :min="0"
            :precision="2"
            style="width: 100%;"
            placeholder="请输入收获数量"
          />
        </el-form-item>

        <el-form-item label="数量单位" prop="harvest_unit">
          <el-select v-model="form.harvest_unit" placeholder="请选择单位" style="width: 100%;">
            <el-option label="千克(kg)" value="kg" />
            <el-option label="吨(ton)" value="ton" />
            <el-option label="袋" value="bag" />
          </el-select>
        </el-form-item>

        <el-form-item label="质量评估">
          <el-select v-model="form.quality_assessment" placeholder="请选择质量等级" style="width: 100%;">
            <el-option label="优秀" value="excellent" />
            <el-option label="良好" value="good" />
            <el-option label="一般" value="average" />
            <el-option label="较差" value="poor" />
          </el-select>
        </el-form-item>

        <el-form-item label="含水量(%)">
          <el-input-number
            v-model="form.moisture_content"
            :min="0"
            :max="100"
            :precision="1"
            style="width: 100%;"
            placeholder="请输入含水量"
          />
        </el-form-item>

        <el-form-item label="处理方式">
          <el-select v-model="form.processing_method" placeholder="请选择处理方式" style="width: 100%;">
            <el-option label="湿处理" value="wet" />
            <el-option label="干处理" value="dry" />
            <el-option label="半干处理" value="semi-dry" />
            <el-option label="蜜处理" value="honey" />
          </el-select>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="3"
            placeholder="记录收获过程中的特殊情况或注意事项"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ isEditing ? '更新' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { growerApi } from '@/services/api'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const loadingPlanting = ref(false)
const harvests = ref([])
const batches = ref([])
const plantingRecords = ref([]) // 可收获的种植记录
const selectedBatch = ref('')
const dateRange = ref<[Date, Date] | null>(null)

// 对话框状态
const showDialog = ref(false)
const showPlantingDialog = ref(false)
const isEditing = ref(false)
const currentHarvest = ref(null)

// 表单引用和数据
const formRef = ref<FormInstance>()
const form = reactive({
  id: '',
  batch_id: '',
  variety: '',
  planting_record_id: '', // 关联的种植记录ID
  harvest_date: '',
  harvest_quantity: 0,
  harvest_unit: 'kg',
  quality_assessment: '',
  moisture_content: 0,
  processing_method: '',
  notes: ''
})

// 表单验证规则
const formRules: FormRules = {
  batch_id: [
    { required: true, message: '请选择批次', trigger: 'change' }
  ],
  harvest_date: [
    { required: true, message: '请选择收获日期', trigger: 'change' }
  ],
  harvest_quantity: [
    { required: true, message: '请输入收获数量', trigger: 'blur' },
    { type: 'number', min: 0, message: '收获数量必须大于0', trigger: 'blur' }
  ],
  harvest_unit: [
    { required: true, message: '请选择数量单位', trigger: 'change' }
  ]
}

// 辅助函数
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: '',
    batch_id: '',
    variety: '',
    planting_record_id: '',
    harvest_date: '',
    harvest_quantity: 0,
    harvest_unit: 'kg',
    quality_assessment: '',
    moisture_content: 0,
    processing_method: '',
    notes: ''
  })
  formRef.value?.clearValidate()
}

// 数据加载函数
const loadBatches = async () => {
  try {
    const response = await growerApi.getBatches()
    if (response.data.success) {
      batches.value = response.data.data.batches || []
      console.log('批次列表加载成功:', batches.value.length, '个批次')
    } else {
      console.error('获取批次列表失败:', response.data.message)
      ElMessage.error(response.data.message || '获取批次列表失败')
    }
  } catch (error) {
    console.error('获取批次列表失败:', error)
    ElMessage.error('获取批次列表失败')
  }
}

// 获取可收获的种植记录
const loadPlantingRecords = async () => {
  try {
    const response = await growerApi.getPlantingRecords()
    if (response.data.success) {
      // 筛选出已完成种植的记录（暂时不检查是否已收获，因为数据库可能没有这个字段）
      plantingRecords.value = response.data.data.records?.filter(record =>
        record.planting_status === '已完成' || record.planting_status === 'completed'
      ) || []

      console.log('可收获的种植记录:', plantingRecords.value.length, '条记录')
    } else {
      console.error('获取种植记录失败:', response.data.message)
      ElMessage.error(response.data.message || '获取种植记录失败')
    }
  } catch (error) {
    console.error('获取种植记录失败:', error)
    ElMessage.error('获取种植记录失败')
  }
}

// 显示种植记录选择对话框
const showPlantingRecords = async () => {
  console.log('显示种植记录对话框')
  console.log('当前种植记录数量:', plantingRecords.value.length)

  loadingPlanting.value = true
  try {
    await loadPlantingRecords()
    console.log('加载完成，种植记录数量:', plantingRecords.value.length)
  } catch (error) {
    console.error('加载种植记录失败:', error)
    ElMessage.error('加载种植记录失败')
  } finally {
    loadingPlanting.value = false
  }

  showPlantingDialog.value = true
}

// 选择种植记录进行收获
const selectPlantingRecord = (record: any) => {
  // 关闭种植记录对话框
  showPlantingDialog.value = false

  // 预填充表单数据
  Object.assign(form, {
    id: '',
    batch_id: record.batch_id,
    variety: record.variety,
    planting_record_id: record.id, // 关联种植记录ID
    harvest_date: new Date().toISOString().split('T')[0],
    harvest_quantity: 0,
    harvest_unit: 'kg',
    quality_assessment: '',
    moisture_content: 0,
    processing_method: '',
    notes: `基于种植记录：${record.batch_id} - ${record.variety}`
  })

  // 显示收获记录对话框
  isEditing.value = false
  showDialog.value = true
}

const loadHarvests = async () => {
  loading.value = true
  try {
    const params: any = {}

    if (selectedBatch.value) {
      params.batch_id = selectedBatch.value
    }

    if (dateRange.value && dateRange.value.length === 2) {
      params.start_date = dateRange.value[0].toISOString().split('T')[0]
      params.end_date = dateRange.value[1].toISOString().split('T')[0]
    }

    const response = await growerApi.getAllHarvestRecords(params)
    if (response.data.success) {
      harvests.value = response.data.data.records || []
      console.log('收获记录加载成功:', harvests.value.length, '条记录')
    } else {
      console.error('获取收获记录失败:', response.data.message)
      ElMessage.error(response.data.message || '获取收获记录失败')
    }
  } catch (error) {
    console.error('获取收获记录失败:', error)
    ElMessage.error('获取收获记录失败')
  } finally {
    loading.value = false
  }
}

// 对话框管理
const showCreateDialog = () => {
  isEditing.value = false
  currentHarvest.value = null
  resetForm()
  showDialog.value = true
}

const closeDialog = () => {
  showDialog.value = false
  resetForm()
}

// CRUD操作
const submitForm = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return
  } catch (error) {
    return
  }

  submitting.value = true
  try {
    let response
    if (isEditing.value) {
      // 更新收获记录 - 需要实现这个API
      response = await fetch(`/api/grower/harvest-records/${form.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(form)
      })
    } else {
      // 创建新的收获记录
      response = await growerApi.recordHarvest(form)
    }

    const data = isEditing.value ? await response.json() : response.data
    if (data.success) {
      ElMessage.success(isEditing.value ? '收获记录更新成功' : '收获记录添加成功')

      // 如果是新增收获记录，需要更新对应的种植记录状态
      if (!isEditing.value && form.planting_record_id) {
        try {
          await updatePlantingRecordStatus(form.planting_record_id)
        } catch (error) {
          console.error('更新种植记录状态失败:', error)
          // 不影响主流程，只记录错误
        }
      }

      closeDialog()
      loadHarvests()
      // 刷新种植记录列表
      loadPlantingRecords()
    } else {
      ElMessage.error(data.message || '操作失败')
    }
  } catch (error: any) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 更新种植记录状态为已收获
const updatePlantingRecordStatus = async (plantingRecordId: string) => {
  const response = await fetch(`/api/grower/planting-records/${plantingRecordId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: JSON.stringify({
      planting_status: '已收获',
      harvested: true,
      harvest_date: new Date().toISOString()
    })
  })

  const data = await response.json()
  if (!data.success) {
    throw new Error(data.message || '更新种植记录状态失败')
  }
}



const editHarvest = (harvest: any) => {
  isEditing.value = true
  currentHarvest.value = harvest

  // 填充表单数据
  Object.assign(form, {
    id: harvest.id,
    batch_id: harvest.batch_id,
    harvest_date: harvest.harvest_date,
    harvest_quantity: harvest.harvest_quantity,
    harvest_unit: harvest.harvest_unit,
    quality_assessment: harvest.quality_assessment,
    moisture_content: harvest.moisture_content,
    processing_method: harvest.processing_method,
    notes: harvest.notes
  })

  showDialog.value = true
}

const deleteHarvest = async (harvest: any) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条收获记录吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await fetch(`/api/grower/harvest-records/${harvest.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    const data = await response.json()
    if (data.success) {
      ElMessage.success('删除成功')
      loadHarvests()
    } else {
      ElMessage.error(data.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    console.log('开始刷新数据...')
    await Promise.all([
      loadBatches(),
      loadPlantingRecords(),
      loadHarvests()
    ])
    console.log('数据刷新完成')
    console.log('种植记录数量:', plantingRecords.value.length)
    console.log('收获记录数量:', harvests.value.length)
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    loading.value = false
  }
}

// 查看详情
const viewDetail = (harvest: any) => {
  ElMessageBox.alert(
    `<div style="text-align: left;">
      <p><strong>批次编号:</strong> ${harvest.batch_id}</p>
      <p><strong>收获日期:</strong> ${formatDate(harvest.harvest_date)}</p>
      <p><strong>收获数量:</strong> ${harvest.harvest_quantity} ${harvest.harvest_unit || 'kg'}</p>
      <p><strong>质量评估:</strong> ${harvest.quality_assessment || '-'}</p>
      <p><strong>含水量:</strong> ${harvest.moisture_content ? harvest.moisture_content + '%' : '-'}</p>
      <p><strong>处理方式:</strong> ${harvest.processing_method || '-'}</p>
      <p><strong>备注:</strong> ${harvest.notes || '-'}</p>
      <p><strong>创建时间:</strong> ${formatDate(harvest.created_at)}</p>
    </div>`,
    '收获记录详情',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确定'
    }
  )
}

// 生命周期
onMounted(async () => {
  console.log('收获管理组件挂载，开始初始化数据...')
  try {
    await refreshData()
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('页面初始化失败，请刷新重试')
  }
})
</script>

<style scoped>
.harvest-management {
  padding: 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin-bottom: 8px;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
}

.page-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 1rem;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  gap: 15px;
}

.search-filters {
  display: flex;
  align-items: center;
  gap: 10px;
}

.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 20px;
}

.records-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.record-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.record-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.record-header {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.variety {
  font-weight: 600;
  font-size: 1.1rem;
}

.record-date {
  font-size: 0.9rem;
  opacity: 0.9;
}

.record-content {
  padding: 20px;
}

.record-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.record-item.full-width {
  flex-direction: column;
}

.record-item .label {
  font-weight: 600;
  color: #7f8c8d;
  min-width: 80px;
  margin-right: 10px;
  font-size: 0.9rem;
}

.record-item.full-width .label {
  margin-bottom: 5px;
}

.record-item .value {
  color: #2c3e50;
  flex: 1;
  word-break: break-word;
}

.record-item .value.highlight {
  font-weight: 600;
  color: #27ae60;
}

.record-actions {
  padding: 15px 20px;
  background: #f8f9fa;
  display: flex;
  gap: 8px;
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin-bottom: 10px;
  color: #2c3e50;
}

.empty-state p {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}


</style>
