<template>
  <div class="simple-planting-records">
    <div class="page-header">
      <h1>种植过程</h1>
      <p>管理您的咖啡种植记录信息</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <button @click="showCreateDialog" class="create-btn">
        <span class="icon">+</span>
        开始种植
      </button>
      <select v-model="selectedBatch" @change="loadRecords" class="batch-filter">
        <option value="">所有批次</option>
        <option v-for="batch in batches" :key="batch.batch_id" :value="batch.batch_id">
          {{ batch.batch_id }} - {{ batch.variety }}
        </option>
      </select>
      <button @click="loadData" class="refresh-btn">
        <span class="icon">↻</span>
        刷新数据
      </button>
    </div>

    <!-- 记录列表 -->
    <div class="record-list">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading">正在加载种植记录...</div>

      <!-- 记录卡片 -->
      <div v-else class="table-container">
        <div v-if="records.length > 0" class="table-wrapper">
          <table class="data-table">
            <thead>
              <tr>
                <th class="text-center">批次编号</th>
                <th class="text-center">咖啡品种</th>
                <th class="text-center">种植方法</th>
                <th class="text-center">土壤类型</th>
                <th class="text-center">灌溉方式</th>
                <th class="text-center">肥料使用</th>
                <th class="text-center">种植时间</th>
                <th class="text-center">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="record in records" :key="record.id" class="table-row">
                <td class="batch-id text-center">{{ record.batch_id }}</td>
                <td class="variety text-center">{{ record.variety }}</td>
                <td class="method text-center">{{ record.planting_method }}</td>
                <td class="soil text-center">{{ record.soil_type }}</td>
                <td class="irrigation text-center">{{ record.irrigation_method }}</td>
                <td class="fertilizer text-center">{{ record.fertilizer_used }}</td>
                <td class="date text-center">{{ formatDate(record.created_at) }}</td>
                <td class="actions text-center">
                  <button @click="viewRecord(record)" class="action-btn view-btn">查看详情</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 空状态 -->
        <div v-if="records.length === 0" class="empty-state">
          <p>暂无种植记录</p>
        </div>
      </div>
    </div>



    <!-- 创建对话框 -->
    <div v-if="createDialogVisible" class="dialog-overlay" @click="closeDialog">
      <div class="dialog" @click.stop>
        <div class="dialog-header">
          <h3>添加种植记录</h3>
          <button @click="closeDialog" class="close-btn">×</button>
        </div>
        <form @submit.prevent="submitRecord" class="dialog-content">
          <div class="form-group">
            <label>选择批次 *</label>
            <select v-model="createForm.batch_id" required>
              <option value="">请选择批次</option>
              <option v-for="batch in batches" :key="batch.batch_id" :value="batch.batch_id">
                {{ batch.batch_id }} - {{ batch.variety }} ({{ batch.origin_location }})
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>种植方法 *</label>
            <select v-model="createForm.planting_method" required>
              <option value="">请选择种植方法</option>
              <option v-for="option in plantingMethodOptions" :key="option" :value="option">
                {{ option }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>土壤类型 *</label>
            <select v-model="createForm.soil_type" required>
              <option value="">请选择土壤类型</option>
              <option v-for="option in soilTypeOptions" :key="option" :value="option">
                {{ option }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>灌溉方式 *</label>
            <select v-model="createForm.irrigation_method" required>
              <option value="">请选择灌溉方式</option>
              <option v-for="option in irrigationMethodOptions" :key="option" :value="option">
                {{ option }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>气候条件 *</label>
            <select v-model="createForm.climate_conditions" required>
              <option value="">请选择气候条件</option>
              <option v-for="option in climateConditionOptions" :key="option" :value="option">
                {{ option }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>肥料使用 *</label>
            <select v-model="createForm.fertilizer_used" required>
              <option value="">请选择使用肥料</option>
              <option v-for="option in fertilizerOptions" :key="option" :value="option">
                {{ option }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>农药使用 *</label>
            <select v-model="createForm.pesticide_used" required>
              <option value="">请选择农药使用情况</option>
              <option v-for="option in pesticideOptions" :key="option" :value="option">
                {{ option }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>备注</label>
            <textarea v-model="createForm.notes" placeholder="其他说明信息"></textarea>
          </div>
          <div class="form-actions">
            <button type="button" @click="closeDialog">取消</button>
            <button type="submit" :disabled="submitting">
              {{ submitting ? '提交中...' : '提交' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 消息提示 -->
    <div v-if="message" :class="['message', messageType]">
      {{ message }}
    </div>

    <!-- 详情弹窗 -->
    <div v-if="showDetailDialog" class="modal-overlay" @click="showDetailDialog = false">
      <div class="detail-modal" @click.stop>
        <div class="detail-header">
          <div class="header-content">
            <h3>种植记录详情</h3>
            <p class="batch-info">{{ selectedRecord?.batch_id }} · {{ selectedRecord?.variety }}</p>
          </div>
          <button class="close-btn" @click="showDetailDialog = false">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div class="detail-content">
          <!-- 基本信息 -->
          <div class="info-section">
            <h4>基本信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">产地</span>
                <span class="value">{{ selectedRecord?.origin_location || '未知' }}</span>
              </div>
              <div class="info-item">
                <span class="label">品种</span>
                <span class="value">{{ selectedRecord?.variety }}</span>
              </div>
              <div class="info-item">
                <span class="label">种植登记时间</span>
                <span class="value">{{ formatDateTime(selectedRecord?.created_at) }}</span>
              </div>
            </div>
          </div>

          <!-- 种植技术 -->
          <div class="info-section">
            <h4>种植技术</h4>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">种植方法</span>
                <span class="value">{{ selectedRecord?.planting_method }}</span>
              </div>
              <div class="info-item">
                <span class="label">土壤类型</span>
                <span class="value">{{ selectedRecord?.soil_type }}</span>
              </div>
              <div class="info-item">
                <span class="label">灌溉方式</span>
                <span class="value">{{ selectedRecord?.irrigation_method }}</span>
              </div>
            </div>
          </div>

          <!-- 农资使用 -->
          <div class="info-section">
            <h4>农资使用</h4>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">肥料使用</span>
                <span class="value">{{ selectedRecord?.fertilizer_used || '无' }}</span>
              </div>
              <div class="info-item">
                <span class="label">农药使用</span>
                <span class="value">{{ selectedRecord?.pesticide_used || '无' }}</span>
              </div>
              <div class="info-item">
                <span class="label">气候条件</span>
                <span class="value">{{ selectedRecord?.climate_conditions || '无' }}</span>
              </div>
            </div>
          </div>

          <!-- 备注信息 -->
          <div v-if="selectedRecord?.notes" class="info-section">
            <h4>备注信息</h4>
            <div class="notes-content">
              {{ selectedRecord.notes }}
            </div>
          </div>
        </div>

        <div class="detail-footer">
          <button class="close-button" @click="showDetailDialog = false">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessageBox } from 'element-plus'
import axios from 'axios'

// 创建独立的axios实例，避免拦截器问题
const directApi = axios.create({
  baseURL: 'http://localhost:3001/api',
  timeout: 10000
})

// 添加认证头
directApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const records = ref([])
const batches = ref([])
const selectedBatch = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

// 对话框状态
const createDialogVisible = ref(false)
const showDetailDialog = ref(false)
const selectedRecord = ref<any>(null)

// 表单数据
const createForm = reactive({
  batch_id: '',
  planting_method: '',
  soil_type: '',
  climate_conditions: '',
  fertilizer_used: '',
  pesticide_used: '',
  irrigation_method: '',
  notes: ''
})

// 常用选项数据
const plantingMethodOptions = [
  '有机种植',
  '传统种植',
  '生态种植',
  '精品种植',
  '阴生种植',
  '日晒种植',
  '水洗处理',
  '蜜处理',
  '日晒处理',
  '其他'
]

const soilTypeOptions = [
  '火山土',
  '红土',
  '黑土',
  '沙质土',
  '粘土',
  '壤土',
  '高原土',
  '其他'
]

const irrigationMethodOptions = [
  '滴灌',
  '喷灌',
  '漫灌',
  '自然降雨',
  '人工浇水',
  '微喷灌',
  '地下灌溉',
  '其他'
]

const climateConditionOptions = [
  '热带气候',
  '亚热带气候',
  '温带气候',
  '高原气候',
  '海洋性气候',
  '大陆性气候',
  '季风气候',
  '其他'
]

const fertilizerOptions = [
  '有机肥',
  '复合肥',
  '氮肥',
  '磷肥',
  '钾肥',
  '生物肥',
  '堆肥',
  '液体肥',
  '无使用',
  '其他'
]

const pesticideOptions = [
  '无使用',
  '生物农药',
  '有机农药',
  '化学农药',
  '杀虫剂',
  '杀菌剂',
  '除草剂',
  '其他'
]

// 消息提示
const message = ref('')
const messageType = ref('info')

// 显示消息
const showMessage = (msg: string, type: 'success' | 'error' | 'info' = 'info') => {
  message.value = msg
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 3000)
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载批次数据
const loadBatches = async () => {
  try {
    const response = await directApi.get('/grower/batches', {
      params: { page: 1, limit: 100 }
    })
    if (response.data.success) {
      batches.value = response.data.data.batches
    } else {
      showMessage('获取批次列表失败: ' + response.data.message, 'error')
    }
  } catch (error: any) {
    console.error('获取批次列表失败:', error)
    showMessage('获取批次列表失败', 'error')
  }
}

// 加载种植记录
const loadRecords = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      batch_id: selectedBatch.value
    }

    const response = await directApi.get('/grower/planting-records', { params })
    if (response.data.success) {
      records.value = response.data.data.records || []
      total.value = response.data.data.pagination?.total || 0
    } else {
      showMessage('获取种植记录失败: ' + response.data.message, 'error')
    }
  } catch (error: any) {
    console.error('获取种植记录失败:', error)
    showMessage('获取种植记录失败', 'error')
  } finally {
    loading.value = false
  }
}

// 加载所有数据
const loadData = async () => {
  await loadBatches()
  await loadRecords()
}

// 分页
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    loadRecords()
  }
}

const nextPage = () => {
  if (currentPage.value < Math.ceil(total.value / pageSize.value)) {
    currentPage.value++
    loadRecords()
  }
}

// 对话框操作
const showCreateDialog = () => {
  createDialogVisible.value = true
}

const closeDialog = () => {
  createDialogVisible.value = false
  // 重置表单
  Object.keys(createForm).forEach(key => {
    createForm[key] = ''
  })
}

// 查看记录详情
const viewRecord = (record: any) => {
  showDetailDialog.value = true
  selectedRecord.value = record
}



// 提交记录
const submitRecord = async () => {
  // 客户端验证
  const requiredFields = [
    { field: 'batch_id', name: '批次' },
    { field: 'planting_method', name: '种植方法' },
    { field: 'soil_type', name: '土壤类型' },
    { field: 'irrigation_method', name: '灌溉方式' },
    { field: 'climate_conditions', name: '气候条件' },
    { field: 'fertilizer_used', name: '肥料使用' },
    { field: 'pesticide_used', name: '农药使用' }
  ]

  for (const { field, name } of requiredFields) {
    if (!createForm[field] || createForm[field].trim() === '') {
      showMessage(`请选择${name}`, 'error')
      return
    }
  }

  submitting.value = true
  try {
    const response = await directApi.post('/grower/planting-records', createForm)
    if (response.data.success) {
      showMessage('种植记录添加成功', 'success')
      closeDialog()
      loadRecords() // 刷新列表
    } else {
      showMessage('添加失败: ' + response.data.message, 'error')
    }
  } catch (error: any) {
    console.error('添加种植记录失败:', error)
    showMessage('添加失败', 'error')
  } finally {
    submitting.value = false
  }
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.simple-planting-records {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin-bottom: 8px;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
}

.page-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 1rem;
}

.action-bar {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  gap: 24px;
}

.create-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.create-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.create-btn .icon {
  font-size: 16px;
  font-weight: bold;
}

.batch-filter {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  min-width: 250px;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.batch-filter:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.batch-filter:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.refresh-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.refresh-btn .icon {
  font-size: 16px;
}

.record-list {
  margin-bottom: 20px;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 14px;
}

.table-container {
  margin-bottom: 20px;
}

.table-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table thead {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.data-table th {
  padding: 16px 20px;
  text-align: center;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  white-space: nowrap;
}

.text-center {
  text-align: center !important;
}

.data-table tbody tr {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
  background: #f9fafb;
}

.data-table tbody tr:last-child {
  border-bottom: none;
}

.data-table td {
  padding: 16px 20px;
  color: #374151;
  vertical-align: middle;
}

.batch-id {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #1f2937;
  font-size: 13px;
}

.variety {
  font-weight: 600;
  color: #1f2937;
  font-size: 15px;
}

.method {
  color: #6b7280;
  font-size: 14px;
}

.soil {
  color: #6b7280;
  font-size: 14px;
}

.irrigation {
  color: #6b7280;
  font-size: 14px;
}

.fertilizer {
  color: #6b7280;
  font-size: 14px;
}

.date {
  color: #6b7280;
  font-size: 13px;
  white-space: nowrap;
}

.actions {
  text-align: center;
  white-space: nowrap;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 2px;
}

.view-btn {
  background: #10b981;
  color: white;
}

.view-btn:hover {
  background: #059669;
}



/* 响应式设计 */
@media (max-width: 1200px) {
  .records-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .records-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .record-card {
    padding: 20px;
    min-height: 240px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .variety {
    margin: 8px 0 0 0;
    font-size: 15px;
  }

  .info-item {
    padding: 6px 0;
  }

  .card-actions {
    justify-content: center;
    gap: 12px;
  }

  .view-btn, .edit-btn {
    flex: 1;
    text-align: center;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 40px;
  background: white;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  color: #6b7280;
}

.empty-state p {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
}



.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.dialog-content {
  padding: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: bold;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  height: 80px;
  resize: vertical;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.form-actions button {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.form-actions button[type="button"] {
  background: #f0f0f0;
  color: #333;
}

.form-actions button[type="submit"] {
  background: #409eff;
  color: white;
}

.form-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  z-index: 1001;
}

.message.success {
  background: #67c23a;
}

.message.error {
  background: #f56c6c;
}

.message.info {
  background: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
  }

  .create-btn {
    width: 100%;
    justify-content: center;
  }

  .batch-filter {
    width: 100%;
    min-width: auto;
  }

  .refresh-btn {
    width: 100%;
    justify-content: center;
  }

  .table-wrapper {
    overflow-x: auto;
  }

  .data-table {
    min-width: 700px;
  }

  .data-table th,
  .data-table td {
    padding: 12px 8px;
    font-size: 12px;
  }

  .batch-id {
    font-size: 11px;
  }

  .variety {
    font-size: 13px;
  }

  .action-btn {
    padding: 4px 8px;
    font-size: 11px;
    margin: 0 1px;
  }
}

/* 详情弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.detail-modal {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 700px;
  max-height: 85vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.detail-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content h3 {
  margin: 0 0 4px 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
}

.batch-info {
  margin: 0;
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #f1f5f9;
  color: #475569;
}

.detail-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.info-section {
  margin-bottom: 32px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-section h4 {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-item .label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.info-item .value {
  font-size: 0.95rem;
  color: #1e293b;
  font-weight: 500;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  min-height: 20px;
}

.notes-content {
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  color: #475569;
  line-height: 1.6;
  font-size: 0.95rem;
}

.detail-footer {
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  display: flex;
  justify-content: center;
}

.close-button {
  background: #475569;
  color: white;
  border: none;
  padding: 12px 32px;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #334155;
  transform: translateY(-1px);
}

/* 详情弹窗响应式设计 */
@media (max-width: 768px) {
  .detail-modal {
    width: 95%;
    margin: 20px;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .detail-header,
  .detail-content,
  .detail-footer {
    padding: 20px;
  }
}
</style>
