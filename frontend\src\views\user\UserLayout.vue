<template>
  <div class="user-layout">
    <!-- 顶部导航栏 -->
    <header class="user-header">
      <div class="nav-container">
        <div class="logo">
          <router-link to="/" class="logo-link">
            <span class="logo-icon">☕</span>
            <span class="logo-text">咖啡豆溯源平台</span>
          </router-link>
        </div>
        <div class="user-info">
          <div class="user-profile">
            <span class="role-icon">{{ getRoleIcon(userRole) }}</span>
            <span class="user-name">{{ userName }}</span>
          </div>
          <button class="logout-button" @click="handleLogout">🚪 退出登录</button>
        </div>
      </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar">
      <nav class="sidebar-nav">
        <!-- 仪表盘 -->
        <router-link v-if="userRole !== 'grower'" to="/dashboard" class="nav-item" exact>
          <span class="nav-icon">📊</span>
          <span class="nav-text">仪表盘</span>
        </router-link>
        <router-link v-if="userRole === 'grower'" to="/dashboard/grower" class="nav-item" exact>
          <span class="nav-icon">📊</span>
          <span class="nav-text">仪表盘</span>
        </router-link>

        <!-- 种植者专用导航 -->
        <template v-if="userRole === 'grower'">
          <div class="nav-section">
            <div class="nav-section-title">种植管理</div>
            <router-link to="/dashboard/grower/batches" class="nav-item" exact>
              <span class="nav-icon">🌱</span>
              <span class="nav-text">种植登记</span>
            </router-link>
            <router-link to="/dashboard/grower/planting" class="nav-item" exact>
              <span class="nav-icon">🌾</span>
              <span class="nav-text">种植过程</span>
            </router-link>
            <router-link to="/dashboard/grower/harvest" class="nav-item" exact>
              <span class="nav-icon">🚜</span>
              <span class="nav-text">种植收获</span>
            </router-link>
          </div>

          <div class="nav-section">
            <div class="nav-section-title">区块链</div>
            <router-link to="/dashboard/grower/blockchain" class="nav-item" exact>
              <span class="nav-icon">⛓️</span>
              <span class="nav-text">上链管理</span>
            </router-link>
            <router-link to="/dashboard/grower/certificates" class="nav-item" exact>
              <span class="nav-icon">📜</span>
              <span class="nav-text">数字证书</span>
            </router-link>
          </div>

          <div class="nav-section">
            <div class="nav-section-title">物流管理</div>
            <router-link to="/dashboard/grower/shipping" class="nav-item" exact>
              <span class="nav-icon">📦</span>
              <span class="nav-text">发货管理</span>
            </router-link>
            <router-link to="/dashboard/grower/logistics" class="nav-item" exact>
              <span class="nav-icon">🚚</span>
              <span class="nav-text">物流跟踪</span>
            </router-link>
          </div>
        </template>

        <!-- 加工商专用导航 -->
        <template v-else-if="userRole === 'processor'">
          <div class="nav-section">
            <div class="nav-section-title">加工管理</div>
            <router-link to="/dashboard/processor/processing" class="nav-item">
              <span class="nav-icon">⚙️</span>
              <span class="nav-text">加工管理</span>
            </router-link>
            <router-link to="/dashboard/processor/quality" class="nav-item">
              <span class="nav-icon">🔍</span>
              <span class="nav-text">质量检测</span>
            </router-link>
          </div>
        </template>

        <!-- 分销商专用导航 -->
        <template v-else-if="userRole === 'distributor'">
          <div class="nav-section">
            <div class="nav-section-title">分销管理</div>
            <router-link to="/dashboard/distributor/inventory" class="nav-item">
              <span class="nav-icon">📦</span>
              <span class="nav-text">库存管理</span>
            </router-link>
            <router-link to="/dashboard/distributor/orders" class="nav-item">
              <span class="nav-icon">📋</span>
              <span class="nav-text">订单管理</span>
            </router-link>
          </div>
        </template>

        <!-- 消费者专用导航 -->
        <template v-else-if="userRole === 'consumer'">
          <div class="nav-section">
            <div class="nav-section-title">消费者服务</div>
            <router-link to="/dashboard/consumer/purchases" class="nav-item">
              <span class="nav-icon">🛒</span>
              <span class="nav-text">购买记录</span>
            </router-link>
            <router-link to="/dashboard/consumer/trace" class="nav-item">
              <span class="nav-icon">🔍</span>
              <span class="nav-text">溯源查询</span>
            </router-link>
          </div>
        </template>

        <!-- 通用功能 -->
        <div class="nav-section">
          <div class="nav-section-title">系统功能</div>
          <router-link to="/dashboard/profile" class="nav-item">
            <span class="nav-icon">👤</span>
            <span class="nav-text">个人资料</span>
          </router-link>
          <router-link to="/dashboard/settings" class="nav-item">
            <span class="nav-icon">⚙️</span>
            <span class="nav-text">系统设置</span>
          </router-link>
        </div>
      </nav>
    </aside>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="content-wrapper">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
// import { useAuthStore } from '@/stores/auth'

const router = useRouter()
// const authStore = useAuthStore()

const userName = ref('用户')
const userRole = ref('grower')

// 角色图标映射
const getRoleIcon = (role: string) => {
  const roleIcons = {
    grower: '🌱',      // 种植者
    processor: '⚙️',   // 加工商
    distributor: '🚚', // 分销商
    consumer: '👤'     // 消费者
  }
  return roleIcons[role as keyof typeof roleIcons] || '👤'
}

// 获取用户信息
const getUserInfo = () => {
  try {
    console.log('UserLayout: 获取用户信息...')
    const token = localStorage.getItem('token')
    const userInfo = JSON.parse(localStorage.getItem('user') || '{}')

    console.log('UserLayout: Token存在:', !!token)
    console.log('UserLayout: 用户信息:', userInfo)

    if (!token || !userInfo.id) {
      console.log('UserLayout: 认证失败，跳转到登录页')
      // 使用setTimeout避免在组件初始化时立即跳转
      setTimeout(() => {
        router.push('/login')
      }, 100)
      return
    }

    // 设置用户信息
    userName.value = userInfo.username || userInfo.real_name || '用户'
    userRole.value = userInfo.role || 'grower'
    console.log('UserLayout: 用户信息设置完成', { userName: userName.value, userRole: userRole.value })
  } catch (error) {
    console.error('UserLayout: 获取用户信息失败:', error)
    setTimeout(() => {
      router.push('/login')
    }, 100)
  }
}

const handleLogout = () => {
  if (confirm('确定要退出登录吗？')) {
    // 清除用户信息
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    // authStore.logout()
    // 跳转到登录页
    router.push('/login')
  }
}

onMounted(() => {
  // 只在首次加载时获取用户信息，避免循环
  if (!userName.value || userName.value === '用户') {
    getUserInfo()
  }
})
</script>

<style scoped>
.user-layout {
  width: 100vw;
  height: 100vh;
  display: grid;
  grid-template-areas:
    "header header"
    "sidebar main";
  grid-template-columns: 320px 1fr;
  grid-template-rows: 80px 1fr;
  background: #f8f9fa;
}

/* 顶部导航栏 */
.user-header {
  grid-area: header;
  background: linear-gradient(90deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 100;
}

.nav-container {
  height: 100%;
  padding: 0 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: white;
}

.logo-icon {
  font-size: 2rem;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.logo-text {
  font-size: 1.3rem;
  font-weight: 700;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 500;
}

.role-icon {
  font-size: 1.2rem;
}

.user-name {
  font-size: 1rem;
}

.logout-button {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.logout-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 侧边栏 */
.sidebar {
  grid-area: sidebar;
  background: white;
  border-right: 1px solid #e9ecef;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05);
}

.sidebar-nav {
  padding: 20px 0;
  height: 100%;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 25px;
}

.nav-section-title {
  padding: 8px 30px;
  font-size: 0.8rem;
  font-weight: 700;
  color: #95a5a6;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 30px;
  color: #7f8c8d;
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  position: relative;
}

.nav-item:hover {
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.08), transparent);
  color: #667eea;
  border-left-color: #667eea;
}

.nav-item.router-link-active,
.nav-item.active {
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.15), transparent);
  color: #667eea;
  border-left-color: #667eea;
  font-weight: 600;
}

.nav-item.router-link-active::after,
.nav-item.active::after {
  content: '';
  position: absolute;
  right: 20px;
  width: 6px;
  height: 6px;
  background: #667eea;
  border-radius: 50%;
}

.nav-icon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.nav-text {
  font-weight: 500;
  font-size: 0.95rem;
}

/* 主要内容区域 */
.main-content {
  grid-area: main;
  overflow-y: auto;
  background: #f8f9fa;
}

.content-wrapper {
  padding: 40px;
  width: 100%;
  margin: 0;
}
</style>
